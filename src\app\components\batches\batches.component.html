<cax-tabView
    mode="segment"
    [pinnedIndexes]="pinnedTabs"
    [(activeIndex)]="activeTabIndex"
    (onClose)="onTabClose($event)"
    [scrollable]="true">
    <cax-tabPanel icon="cax cax-document-text">
        <div class="action-container">
            <div class="search-container">
                <cax-autoComplete
                    [(ngModel)]="selectedItem"
                    [suggestions]="filteredItems"
                    (completeMethod)="search($event)"
                    field="title"
                    [showClear]="true"
                    [isloading]="isLoading"
                    [style]="{ width: '100%' }"
                    [placeholder]="'Search Batch...'"
                    [group]="true"
                    (onSelect)="onItemSelect($event)">
                    <ng-template let-group caxTemplate="group">
                        <div class="autocomplete-group-item">
                            <span>{{ group.label }}</span>
                        </div>
                    </ng-template>
                    <ng-template let-item caxTemplate="item">
                        <div class="autocomplete-item">
                            <div class="item-list">
                                <div class="item-title">
                                    <i class="cax cax-history"></i>
                                    <span>{{ item.title }}</span>
                                </div>
                            </div>
                            <span class="batchId" *ngIf="item.batchId">
                                {{ item.batchId }}
                            </span>
                        </div>
                    </ng-template>
                    <div class="line"></div>
                </cax-autoComplete>
            </div>
            <div class="button-container">
                <cax-button
                    *ngIf="!object.keys(appliedTableFilters)?.length"
                    severity="secondary"
                    icon="cax cax-tuning"
                    (click)="filtersPanel.toggle($event)"></cax-button>
                <cax-button
                    *ngIf="object.keys(appliedTableFilters)?.length"
                    severity="secondary"
                    icon="cax cax-tuning"
                    caxBadge
                    [value]="object.keys(appliedTableFilters).length"
                    badgeSize="xs"
                    badgeSeverity="danger"
                    (click)="filtersPanel.toggle($event)"></cax-button>
                <cax-button
                    (click)="tableSizePanel.toggle($event)"
                    severity="secondary"
                    icon="cax cax-text-icon-bold"></cax-button>
                <cax-button
                    (click)="openColumnsList()"
                    severity="secondary"
                    icon="cax cax-layers"></cax-button>
                <cax-button
                    (click)="openUploadSidebar()"
                    leftIcon="cax cax-upload"
                    label="Upload New Batch"></cax-button>
            </div>
        </div>
        <div class="table-container">
            <cax-table
                [columns]="sortedSelectedColumns"
                styleClass="cax-datatable-gridlines"
                [value]="batchList"
                [scrollable]="true"
                [fontSize]="batchFontSize"
                [rowSize]="batchRowSize"
                [filters]="batchTableFilters"
                [editMode]="'cell'"
                filterType="custom"
                sortType="custom"
                sortMode="multiple"
                [(selection)]="selectedBatches"
                (selectionChange)="onTableSelectionChange($event)"
                (onSort)="onTableSort($event)"
                (onFilter)="onTableFilter($event)"
                [loading]="isLoadingBatches">
                <ng-template caxTemplate="header" let-columns>
                    <tr>
                        <th
                            caxColumnWidth
                            [minWidth]="'60px'"
                            [maxWidth]="'60px'"
                            caxFrozenColumn>
                            <cax-tableHeaderCheckbox />
                        </th>
                        <th
                            [caxSortableColumn]="col.field"
                            caxColumnWidth
                            [minWidth]="col.minWidth"
                            [maxWidth]="col.maxWidth"
                            caxFrozenColumn
                            [frozen]="col.fixed"
                            [alignFrozen]="col.position ? col.position : 'left'"
                            *ngFor="let col of columns">
                            {{ col.header }}
                            <cax-columnFilter
                                *ngIf="col.filter"
                                [field]="col.field" />
                            <cax-sortIcon
                                *ngIf="col.sortable"
                                [field]="col.field" />
                        </th>
                    </tr>
                </ng-template>
                <ng-template
                    caxTemplate="body"
                    let-rowData
                    let-index="rowIndex"
                    let-columns="columns">
                    <tr>
                        <td caxFrozenColumn>
                            <cax-tableCheckbox
                                [index]="index"
                                [value]="rowData" />
                        </td>
                        <td
                            caxFrozenColumn
                            [frozen]="col.fixed"
                            [alignFrozen]="col.position ? col.position : 'left'"
                            *ngFor="let col of columns">
                            <div
                                class="w-100 h-100 data-container"
                                [ngSwitch]="col.field">
                                <!-- case for batch name -->
                                <div
                                    *ngSwitchCase="'name'"
                                    class="d-flex align-center spc-btwn batch-name">
                                    <span (click)="openBatchDetails(rowData)">{{
                                        rowData[col.field]
                                    }}</span>
                                    <i
                                        *ngIf="rowData.audit_log?.length"
                                        class="cax cax-layers-minimalistic"
                                        (click)="
                                            openBatchLogPanel($event, rowData)
                                        "></i>
                                </div>
                                <!-- case for batch status -->
                                <div *ngSwitchCase="'status'">
                                    <cax-chip
                                        (click)="
                                            openBatchStatusPanel(
                                                $event,
                                                rowData[col.field],
                                                index
                                            )
                                        "
                                        [label]="
                                            getBatchStatus(rowData[col.field])
                                                .name
                                        "
                                        [severity]="
                                            getBatchStatus(rowData[col.field])
                                                .severity
                                        "
                                        [icon]="
                                            getBatchStatus(rowData[col.field])
                                                .icon
                                        "
                                        [size]="size"></cax-chip>
                                </div>
                                <!-- sitch case for the eta -->
                                <div
                                    *ngSwitchCase="'eta'"
                                    style="cursor: pointer"
                                    class="w-100 h-100"
                                    (click)="onDateClick($event, rowData)">
                                    <span>
                                        {{
                                            rowData.eta
                                                ? (rowData.eta
                                                  | date: 'dd/MM/yyyy')
                                                : ''
                                        }}
                                    </span>
                                    <cax-overlayPanel
                                        #calendalPanel
                                        [showCloseIcon]="false"
                                        [dismissable]="true"
                                         styleClass="no-padding-overlay"
                                        [style]="{ width: 'inherit'}">
                                        <cax-calendar
                                            [(ngModel)]="selectedDate"
                                            [inline]="true"
                                            inputId="date2"
                                            (onInlineSaved)="handleDateSaved()"
                                            (onInlineCancelled)="
                                                handleDateCancel()
                                            ">
                                        </cax-calendar>
                                    </cax-overlayPanel>
                                </div>
                                <!-- download column -->
                                <div *ngSwitchCase="'download'">
                                    <cax-splitButton
                                        *ngSwitchCase="'download'"
                                        [icon]="'cax cax-download'">
                                    </cax-splitButton>
                                </div>
                                <!-- case for comments -->
                                <div
                                    *ngSwitchCase="'comments'"
                                    class="comments-container"
                                    (click)="openComments(rowData)">
                                    <!-- Custom avatar for the comment sender -->
                                    <cax-avatar
                                        [label]="
                                            rowData.comments[0].sender
                                                .substring(0, 2)
                                                .toUpperCase()
                                        "
                                        [avatarSize]="avatarSize"
                                        [shape]="'circle'"
                                        [style]="{
                                            marginRight: '8px',
                                        }"></cax-avatar>

                                    <!-- Comment text -->
                                    <div class="comment-text">
                                        {{
                                            rowData.comments?.length
                                                ? (rowData.comments[
                                                      rowData.comments.length -
                                                          1
                                                  ].text | slice: 0 : 30) +
                                                  (rowData.comments[
                                                      rowData.comments.length -
                                                          1
                                                  ].text?.length > 30
                                                      ? '...'
                                                      : '')
                                                : 'No comments'
                                        }}
                                    </div>
                                </div>
                                <!-- case for references -->
                                <div
                                        [caxEditableColumn]="col"
                                        [caxEditableColumnField]="col.field"
                                        *ngSwitchCase="'references'"
                                        class="references-container">
                                    <cax-cellEditor>
                                        <ng-template caxTemplate="input">
                                            <input
                                                caxInputText
                                                type="text"
                                                [(ngModel)]="rowData.references" />
                                        </ng-template>

                                        <ng-template caxTemplate="output">
                                            <div *ngFor="let ref of rowData[col.field]">
                                            <a href="{{ ref }}" target="_blank"
                                                >• {{ ref }}</a>
                                            </div>
                                        </ng-template>
                                    </cax-cellEditor>
                                </div>

                                <!-- case for tags -->
                                <div *ngSwitchCase="'tags'" class="tags-container" (click)="handleOverlay($event, rowData, null)" >
                                    <cax-chip
                                    *ngFor="let tag of rowData[col.field]"
                                    [label]="tag.name"
                                    [severity]="tag.severity"
                                    [removable]="tag.removable"
                                    [size]="size"
                                    (onRemove)="handleTagRemove($event, rowData, tag)"
                                    >
                                    </cax-chip>
                                <cax-overlayPanel
                                        #tagsPanel
                                        [showCloseIcon]="false"
                                        [dismissable]="true"
                                    >
                                        <div class="tag-container">
                                            <ng-container *ngIf="overlayTags.length > 0; else noTagsTemplate">
                                                <cax-inputtext
                                                    [placeholder]="'Search'"
                                                    [size]="inputSize"
                                                    [leftIcon]="true"
                                                    [clearIcon]="true"
                                                    [(ngModel)]="tagsName"
                                                    (ngModelChange)="onTagSearch($event)"
                                                    [leftIconClass]="'cax cax-magnifier'"
                                                ></cax-inputtext>

                                                <div class="chip-tag">
                                                    <cax-chip
                                                        *ngFor="let tag of filteredTags"
                                                        [label]="tag.name"
                                                        [removable]="false"
                                                        [size]="size"
                                                        [severity]="tag.severity"
                                                        (click)="updateTag(tag)"
                                                    ></cax-chip>
                                                </div>
                                                <cax-divider [style]="{ margin: '4px 0' }"></cax-divider>
                                                <div class="create-tag-button">
                                                    <cax-button
                                                        [leftIcon]="'cax cax-add'"
                                                        [label]="'Create New Tag'"
                                                        [severity]="'primary'"
                                                        [link]="true"
                                                        [size]="buttonSize"
                                                        (click)="openCreateTagSidebar()"
                                                    ></cax-button>
                                                </div>
                                            </ng-container>

                                            <ng-template #noTagsTemplate>
                                                <div class="no-tags-wrapper">
                                                    <cax-inputtext
                                                    [placeholder]="'Search'"
                                                    [size]="inputSize"
                                                    [leftIcon]="true"
                                                    [clearIcon]="true"
                                                    [(ngModel)]="tagsName"
                                                    (ngModelChange)="onTagSearch($event)"
                                                    [leftIconClass]="'cax cax-magnifier'"
                                                  ></cax-inputtext>
                                                    <div class="empty-tag">

                                                    <i class="cax cax-tag-bold custom-icon"></i>

                                                    <div class="no-tags-heading">No Tags Found</div>
                                                    <div class="no-tags-desc">
                                                        Tags help organize and categorize batches. You can add new tags to get started.
                                                    </div>
                                                    </div>
                                                    <cax-divider [style]="{ margin: '4px 0' }"></cax-divider>
                                                    <div class="create-tag-button">
                                                        <cax-button
                                                            [leftIcon]="'cax cax-add'"
                                                            [label]="'Create New Tag'"
                                                            [severity]="'primary'"
                                                            [link]="true"
                                                            [size]="buttonSize"
                                                            (click)="openCreateTagSidebar()"
                                                        ></cax-button>
                                                    </div>
                                                </div>
                                            </ng-template>
                                        </div>
                                    </cax-overlayPanel>

                                </div>

                                <!-- case for accepted rows -->
                                <div
                                    *ngSwitchCase="'accepted_rows'"
                                    class="accepted-rows-container">
                                    <span class="fw-bold">{{
                                        rowData[col.field]
                                    }}</span
                                    ><span class="fw-light"
                                        >/{{ rowData.total_rows }}</span
                                    >
                                </div>
                                <!-- case for uploaded_by -->
                                <div
                                    *ngSwitchCase="'uploaded_by'"
                                    class="user-container">
                                    <cax-avatar
                                        [label]="
                                            rowData[col.field]
                                                .substring(0, 2)
                                                .toUpperCase()
                                        "
                                        [avatarSize]="avatarSize"
                                        [shape]="'circle'">
                                    </cax-avatar>
                                </div>
                                <!-- case for assignee -->
                                <div
                                    *ngSwitchCase="'assignee'"
                                    class="user-container">
                                    <cax-avatar
                                        [label]="
                                            rowData[col.field]
                                                .substring(0, 2)
                                                .toUpperCase()
                                        "
                                        [avatarSize]="avatarSize"
                                        [shape]="'circle'">
                                    </cax-avatar>
                                </div>
                                <!-- case for description -->
                                 <div
                                     [caxEditableColumn]="col"
                                     [caxEditableColumnField]="col.field"
                                    *ngSwitchCase="'description'"
                                    class="user-container">
                                    <cax-cellEditor>
                                        <ng-template caxTemplate="input">
                                            <input
                                                caxInputText
                                                type="text"
                                                [(ngModel)]="rowData.description" />
                                        </ng-template>

                                        <ng-template caxTemplate="output">
                                            <div >
                                             {{rowData.description}}
                                            </div>
                                        </ng-template>
                                    </cax-cellEditor>
                                </div>
                                <!-- case for process -->
                                  <div
                                     [caxEditableColumn]="col"
                                     [caxEditableColumnField]="col.field"
                                     *ngSwitchCase="'process'"
                                     class="w-100 h-100"
                                    class="user-container">
                                    <cax-cellEditor>
                                        <ng-template caxTemplate="input">

                                                <input
                                                caxInputText
                                                type="text"
                                                [(ngModel)]="rowData.process" />
                                        </ng-template>

                                        <ng-template caxTemplate="output">
                                            <div >
                                             {{rowData.process}}
                                            </div>
                                        </ng-template>
                                    </cax-cellEditor>
                                </div>
                                <!-- default case -->
                                <div *ngSwitchDefault>
                                    {{ rowData[col.field] }}
                                </div>
                            </div>
                        </td>
                    </tr>
                </ng-template>
            </cax-table>
        </div>
        <div class="paginator-container">
            <cax-paginator
                [pageLinkSize]="5"
                [currentPageReportTemplate]="
                    'Showing {currentPage} to {totalPages} of {totalRecords} enteries'
                "
                [showCurrentPageReport]="true"
                [showFirstLastIcon]="true"
                [totalRecords]="3"
                [rows]="15"
                [rightAligned]="true"
                [rowsPerPageOptions]="[15, 30, 50, 100]"
                [showJumpToPageInput]="true"
                [showPageLinks]="true"></cax-paginator>
        </div>
    </cax-tabPanel>
    <cax-tabPanel
        *ngFor="let tab of dynamicTabs"
        [closable]="tab.closable"
        [header]="tab.header">
        <ng-container *ngComponentOutlet="tab.component; inputs: tab.data" />
    </cax-tabPanel>
</cax-tabView>

<!-- column list sidebar -->

<cax-sidebar
    [headerText]="'Columns (' + batchTableColumns.length + ')'"
    showCloseIcon="true"
    position="right"
    [style]="{ width: '390px' }"
    [(visible)]="columnSidebarVisible">
    <app-column-list-sidebar
        [columnListData]="batchTableColumns"
        (emitFilteredColumns)="
            filterColumnsList($event)
        "></app-column-list-sidebar>
</cax-sidebar>

<!-- status overlay panel -->

<cax-overlayPanel
    #batchStatusPanel
    [style]="{ width: 'inherit' }"
    [dismissable]="true">
    <div class="batch-status-panel">
        <cax-chip
            *ngFor="let status of batchStatusPanelList"
            [label]="status.name"
            [size]="size"
            [severity]="status.severity"
            [icon]="status.icon"
            (click)="updateBatchStatus(status)"></cax-chip>
    </div>
</cax-overlayPanel>

<!-- batch log overlay panel -->

<cax-overlayPanel
    [style]="{ width: 'inherit' }"
    #batchLogPanel
    [dismissable]="true">
    <cax-timeline [value]="batchLogList">
        <ng-template caxTemplate="content" let-event>
            {{ event }}
        </ng-template>
    </cax-timeline>
</cax-overlayPanel>

<!-- table configuration overlay panel -->

<cax-overlayPanel
    [style]="{ width: '478px' }"
    #tableSizePanel
    [dismissable]="true">
    <cax-tableconfiguration
        (fontSizeChange)="onFontSizeChange($event)"
        (rowHeightChange)="onRowHeightChange($event)"></cax-tableconfiguration>
</cax-overlayPanel>

<!-- filters list overlay panel -->

<cax-overlayPanel
    [style]="{ width: '332px' }"
    #filtersPanel
    [dismissable]="true">
    <app-filters-list-panel
        [filtersApplied]="appliedTableFilters"></app-filters-list-panel>
</cax-overlayPanel>

<!-- upload new batch sidebar -->

<cax-sidebar
    [headerText]="'Upload New Batch'"
    showCloseIcon="true"
    position="right"
    [style]="{ width: '558px' }"
    [(visible)]="uploadSidebarVisible">
    <app-upload-batch-sidebar></app-upload-batch-sidebar>
    <ng-template caxTemplate="footer">
        <div class="upload-button-container">
            <cax-button
                [style]="{ width: '100%' }"
                label="Cancel"
                (click)="uploadSidebarVisible = false"
                severity="secondary"
                outlined="true"></cax-button>
            <cax-button [style]="{ width: '100%' }" label="Upload"></cax-button>
        </div>
    </ng-template>
</cax-sidebar>

<!-- multiple selection toolbar -->

<cax-sidebar
    [style]="{
        left: '50%',
        width: 'max-content',
        transform: 'translate(-50%, 0)',
        height: 'auto',
    }"
    [headerStyle]="{
        background: 'var(--neutral-800)',
        color: 'var(--white-100)',
    }"
    [position]="'bottom'"
    [headerText]="selectedBatches.length + ' Selected'"
    [modal]="false"
    [dismissible]="false"
    [(visible)]="selectionSidebarVisible">
    <div class="selection-sidebar-content">
        <div class="fields-container">
            <!-- Status -->
            <div class="input-container">
                <cax-dropdown
                    [labelText]="'Status'"
                    [appendTo] = "'body'"
                    [options]="statusOptions"
                    [(ngModel)]="selectedBatchStatus"
                    [placeholder]="'Change Status'"
                    [style]="{ width: '180px' }"></cax-dropdown>
            </div>

            <!-- ETA -->
            <div class="input-container">
                <cax-inputtext
                    label="Update ETA"
                    [placeholder]="'Change ETA'"
                    [(ngModel)]="selectedBatchETA"
                    [style]="{ width: '180px' }"></cax-inputtext>
            </div>

            <!-- Assignee -->
            <div class="input-container">
                <cax-dropdown
                    [labelText]="'Assignee'"
                    [appendTo] = "'body'"
                    [options]="assigneeOptions"
                    [(ngModel)]="selectedBatchAssignee"
                    [placeholder]="'Change Assignee'"
                    [style]="{ width: '180px' }"></cax-dropdown>
            </div>

            <!-- Comments -->
            <div class="input-container">
                <cax-inputtext
                    [label]="'Add Comments'"
                    [placeholder]="'Enter your comments here...'"
                    [(ngModel)]="selectedBatchesComment"
                    [style]="{ width: '300px' }"></cax-inputtext>
            </div>

            <!-- Buttons -->
            <div class="button-wrapper">
                <cax-button
                    label="Save"
                    [disabled]="!isSaveEnabled()"
                    (click)="saveAllChanges()"
                    [size]="'medium'">
                </cax-button>

                <div class="divider"></div>

                <cax-splitButton icon="cax cax-download" label="">
                </cax-splitButton>
            </div>
        </div>
    </div>
</cax-sidebar>

<!-- reason for rework dialog  -->

<cax-dialog
    [(visible)]="reworkDialogVisible"
    [closable]="false"
    [modal]="true"
    [style]="{ width: '528px' }">
    <app-rework-dialog
        (reworkDialogStatus)="reworkDialogVisible = $event"></app-rework-dialog>
</cax-dialog>

<!-- common confirm dialog -->

<cax-confirmDialog></cax-confirmDialog>

<!-- comments sidebar -->
<cax-sidebar
     [headerStyle]="{
        fontSize: '16px',
    }"
    [headerText]="currentBatchHeader"
    showCloseIcon="true"
    position="right"
    [style]="{ width: '480px' }"
    [(visible)]="openComment"
>
    <cax-comments
        [comments]="currentBatchComments"
        [sidebarHeader]="currentBatchHeader"
        [mentionSuggestions]="mentionSuggestions"
        [hashtagSuggestions]="hashtagSuggestions"
        [isAdmin]="true"
        (commentAdded)="onCommentAdded($event)"
        [(visible)]="commentsSidebarVisible">
    </cax-comments>
</cax-sidebar>


<!-- Tag Sidebar -->
<app-create-new-tag-sidebar
    [(visible)]="isCreateTagSidebarOpen">
</app-create-new-tag-sidebar>
